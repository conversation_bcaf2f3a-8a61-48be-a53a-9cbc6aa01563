import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { stroke, then, ThenSelector, vertex } from '../selectors';
import { GeometryTool } from './geo.tool';
import { getFocusDocCtrl, handleIfPointerNotInError } from './tool.utils';
import { performConstructionWithVertex } from './parallel_perpendicular.line.tool.utils';

/**
 * Perpendicular Line Tool - Creates perpendicular lines using selector pattern
 * <AUTHOR>
 */
export class CreatePerpendicularLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreatePerpendicularLineTool';

    declare selLogic: ThenSelector;
    private pQ = new PreviewQueue();
    private selectedLine: RenderLine | undefined;
    private selectedPoint: RenderVertex | undefined;
    private previewLine: RenderLine | undefined;
    private isShowingPreview = false;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        this.selLogic?.reset();
        this.selectedLine = undefined;
        this.selectedPoint = undefined;
        this.previewLine = undefined;
        this.isShowingPreview = false;
        super.resetState();
    }

    /**
     * Creates the selection logic: select line first, then point, then show preview and select final vertex
     */
    private createSelLogic() {
        // First select a line
        const lineSelector = stroke({
            selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Then select a point
        const pointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Finally select a vertex for construction
        const finalVertexSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Main selection logic: select line, then point, then show preview and select final vertex
        this.selLogic = then([lineSelector, pointSelector, finalVertexSelector], {
            onPartialSelection: (currentSelected: any, allSelected: any[], selector: ThenSelector, doc: GeoDocCtrl) => {
                if (allSelected && allSelected.length === 2) {
                    // After selecting line and point, show preview and continue selection
                    this.selectedLine = allSelected[0] as RenderLine;
                    this.selectedPoint = allSelected[1] as RenderVertex;
                    this.showPreviewLine(doc);
                    return true; // Continue selection
                }
                return true;
            },
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const selected = selector.selected;
                if (!selected || selected.length < 3) return;

                this.selectedLine = selected[0] as RenderLine;
                this.selectedPoint = selected[1] as RenderVertex;
                const finalVertex = selected[2] as RenderVertex;

                // Use the utility function to perform construction based on final vertex position
                await this.performConstructionWithFinalVertex(doc, finalVertex);
            },
        });
    }

    /**
     * Shows preview of the perpendicular line after selecting line and point
     */
    private showPreviewLine(ctrl: GeoDocCtrl) {
        if (!this.selectedLine || !this.selectedPoint) return;

        // Calculate perpendicular line vector (rotate original line vector by 90 degrees)
        const lineVector = this.selectedLine.vector;
        const perpendicularVector = [-lineVector[1], lineVector[0]];

        // Create preview line through the selected point with perpendicular vector
        this.previewLine = pLine(
            ctrl,
            -31, // Preview ID for perpendicular line
            RenderLine,
            this.selectedPoint,
            undefined,
            perpendicularVector
        );

        this.pQ.add(this.previewLine);
        this.isShowingPreview = true;
    }

    /**
     * Performs construction using the utility function based on final vertex selection
     */
    private async performConstructionWithFinalVertex(ctrl: GeoDocCtrl, finalVertex: RenderVertex) {
        if (!this.selectedLine || !this.selectedPoint) return;

        // Use the new utility function to handle construction logic
        await performConstructionWithVertex(
            this,
            ctrl,
            {
                point: this.selectedPoint,
                line: this.selectedLine,
                finalVertex: finalVertex,
                buildLineSegmentWithIntersectLine: this.buildLineSegmentWithIntersectLine.bind(this),
                buildLineSegment: this.buildLineSegment.bind(this),
                buildLine: this.buildLine.bind(this),
            },
            true // isPerpendicularVector = true for perpendicular lines
        );
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: any, ctrl: GeoDocCtrl) {
        // Continue with selection logic
        const selected = this.selLogic.trySelect(event, ctrl);

        // If we have line and point selected but not showing preview yet, show it
        if (selected && selected.length === 2 && !this.isShowingPreview) {
            this.selectedLine = selected[0] as RenderLine;
            this.selectedPoint = selected[1] as RenderVertex;
            this.showPreviewLine(ctrl);
        }

        this.pQ.flush(ctrl);
    }

    private buildLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'ThoughPointPerpendicularWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegment(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string,
        k: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'PerpendicularWithNewPoint'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegmentWithIntersectLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        intersectionLineName: string,
        intersectionLineType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'PerpendicularWithIntersectLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
