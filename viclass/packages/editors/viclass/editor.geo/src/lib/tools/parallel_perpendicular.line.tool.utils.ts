import { vector } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderVertex } from '../model';
import { GeoPointerEvent } from '../model/geo.models';
import { GeoSelectHitContext } from '../objects';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPreviewVertexRenderProp,
    calculateScalingFactor,
    calculateUnitVector,
    isElementLine,
    isSamePoint,
    pickPointName,
    projectPointOntoLine,
    requestElementNames,
} from './tool.utils';

/**
 * Shows preview for parallel or perpendicular line based on selected line and point
 * This is a simplified implementation that works with the new selector pattern
 * @param _geoTool - The geometry tool instance (unused in this simplified version)
 * @param _event - Pointer event (can be null for static preview)
 * @param _object - Object containing the selected line and point
 * @param _isPerpendicularVector - Whether to show perpendicular (true) or parallel (false) line
 */
export function previewPointerMove(
    _geoTool: GeometryTool<CommonToolState>,
    _event: GeoPointerEvent | null,
    _object: { point: RenderVertex; line: RenderLine },
    _isPerpendicularVector: boolean = false
) {
    // This function is now handled by the selector pattern in the tools themselves
    // The preview is managed by the PreviewQueue in each tool
    // This function is kept for compatibility but does nothing
    return;
}

/**
 * Performs construction based on selected line, point, and final vertex position
 * This is a new function that works with the selector-based workflow
 */
export async function performConstructionWithVertex(
    geoTool: GeometryTool<CommonToolState>,
    ctrl: GeoDocCtrl,
    object: {
        point: RenderVertex;
        line: RenderLine;
        finalVertex: RenderVertex;
        buildLineSegmentWithIntersectLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            intersectionLineName: string,
            intersectionLineType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
        buildLineSegment: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string,
            k: number
        ) => GeoElConstructionRequest;
        buildLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
    },
    isPerpendicularVector: boolean = false
) {
    const finalVertexCoords = object.finalVertex.coords;

    // Check if the final vertex is on a line (for intersection construction)
    // This would need to be determined by checking if the vertex lies on any existing line
    // For now, we'll use the simple construction logic

    let constructionAngle = undefined;

    const startPointName = object.point.name;
    const startLineName = object.line.name;
    const startLineType = object.line.elType;

    const vS = object.line.vector;
    const v = isPerpendicularVector ? [-vS[1], vS[0]] : vS;
    const startPoint = object.point.coords;
    const projectPoint = projectPointOntoLine(finalVertexCoords, startPoint, v);

    const vertex2: RenderVertex = {
        relIndex: -12,
        type: 'RenderVertex',
        elType: 'Point',
        renderProp: buildPreviewVertexRenderProp(),
        name: undefined,
        coords: projectPoint,
        usable: true,
        valid: true,
    };

    const isPointerAtStartPosition = isSamePoint(finalVertexCoords, object.point.coords, ctrl);
    const isPointerOnPerpendicularLine = isSamePoint(finalVertexCoords, projectPoint, ctrl);
    const nt = geoTool.toolbar.getTool('NamingElementTool') as NamingElementTool;

    // Choose construction type based on final vertex position
    if (!isPointerAtStartPosition && isPointerOnPerpendicularLine) {
        // Create line segment
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Đoạn Thẳng',
                    originElement: [object.point, vertex2],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            geoTool.resetState();
            return;
        }

        const uVector = calculateUnitVector(v);
        const k = calculateScalingFactor(uVector, startPoint, projectPoint);
        const lVector = [projectPoint[0] - startPoint[0], projectPoint[1] - startPoint[1]];

        const lineDirection =
            vector(v[0], v[1]).angleTo(vector(lVector[0], lVector[1])) * (180 / Math.PI) == 180 ? -1 : 1;

        constructionAngle = object.buildLineSegment(
            inputPointNames.join(''),
            startLineName,
            startLineType,
            startPointName,
            k * lineDirection
        );
    } else {
        // Create straight line
        constructionAngle = object.buildLine('', startLineName, startLineType, startPointName);
    }

    geoTool.resetState();

    await ctrl.editor.awarenessFeature.useAwareness(
        ctrl.viewport.id,
        'Đang tạo hình',
        buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        async () => {
            const constructResponse = await geoTool.editor.geoGateway.construct(ctrl.state.globalId, [
                {
                    construction: constructionAngle,
                },
            ]);

            await syncRenderCommands(constructResponse.render, ctrl);
            addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            geoTool.resetState();
        }
    );
}

export async function onFinalClick(
    geoTool: GeometryTool<CommonToolState>,
    event: GeoPointerEvent,
    object: {
        point: RenderVertex;
        line: RenderLine;
        lastHitCtx: GeoSelectHitContext | null;
        buildLineSegmentWithIntersectLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            intersectionLineName: string,
            intersectionLineType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
        buildLineSegment: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string,
            k: number
        ) => GeoElConstructionRequest;
        buildLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
    },
    isPerpendicularVector: boolean = false
) {
    const { ctrl, pos, hitCtx, hitEl } = geoTool.posAndCtrl(event);
    let mpg = [pos.x, pos.y];
    let intersectLine: RenderLine;

    if (hitEl) {
        ctrl.editor.selectElement(hitCtx, true);

        if (hitEl.type == 'RenderVertex') {
            const e = hitEl as RenderVertex;
            mpg = e.coords;
        }

        if (isElementLine(hitEl)) {
            intersectLine = hitEl as RenderLine;
        }
    }

    let constructionAngle = undefined;

    const startPointName = object.point.name;
    const startLineName = object.line.name;
    const startLineType = object.line.elType;

    const vS = object.line.vector;
    const v = isPerpendicularVector ? [-vS[1], vS[0]] : vS;
    const startPoint = object.point.coords;
    const projectPoint = projectPointOntoLine(mpg, startPoint, v);

    let projectPointIntersect: number[];

    if (intersectLine) {
        const vI = intersectLine.vector;
        const v1 = [-vI[1], vI[0]];
        projectPointIntersect = projectPointOntoLine(mpg, startPoint, v1);
    }

    const vertex2: RenderVertex = {
        relIndex: -12,
        type: 'RenderVertex',
        elType: 'Point',
        renderProp: buildPreviewVertexRenderProp(),
        name: undefined,
        coords: projectPoint,
        usable: true,
        valid: true,
    };

    const haveIntersectLine = !!intersectLine;
    const isPointStartOnIntersectLine = isSamePoint(object.point.coords, projectPointIntersect, ctrl);
    const isPointerAtStartPosition = isSamePoint(mpg, object.point.coords, ctrl);
    const isPointerOnPerpendicularLine = isSamePoint(mpg, projectPoint, ctrl);
    const nt = geoTool.toolbar.getTool('NamingElementTool') as NamingElementTool;
    // Selected 2nd line and start point is not on 2nd line (create line segment)
    if (haveIntersectLine && !isPointStartOnIntersectLine) {
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Đoạn Thẳng',
                    originElement: [object.point, vertex2],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            geoTool.resetState();
            return;
        }

        const intersectionLineName = intersectLine.name;
        constructionAngle = object.buildLineSegmentWithIntersectLine(
            inputPointNames.join(''),
            startLineName,
            startLineType,
            intersectionLineName,
            intersectLine.elType,
            startPointName
        );
        // choose a point on the perpendicular line and the chosen point is not the starting point (create line segment)
    } else if (!isPointerAtStartPosition && isPointerOnPerpendicularLine) {
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Đoạn Thẳng',
                    originElement: [object.point, vertex2],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            geoTool.resetState();
            return;
        }

        const uVector = calculateUnitVector(v);
        const k = calculateScalingFactor(uVector, startPoint, projectPoint);
        const lVector = [projectPoint[0] - startPoint[0], projectPoint[1] - startPoint[1]];

        const lineDirection =
            vector(v[0], v[1]).angleTo(vector(lVector[0], lVector[1])) * (180 / Math.PI) == 180 ? -1 : 1;

        constructionAngle = object.buildLineSegment(
            inputPointNames.join(''),
            startLineName,
            startLineType,
            startPointName,
            k * lineDirection
        );
        // create straight line
    } else constructionAngle = object.buildLine('', startLineName, startLineType, startPointName);

    geoTool.resetState();

    await ctrl.editor.awarenessFeature.useAwareness(
        ctrl.viewport.id,
        'Đang tạo hình',
        buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        async () => {
            const constructResponse = await geoTool.editor.geoGateway.construct(ctrl.state.globalId, [
                {
                    construction: constructionAngle,
                },
            ]);

            await syncRenderCommands(constructResponse.render, ctrl);
            addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            geoTool.resetState();
        }
    );
}
